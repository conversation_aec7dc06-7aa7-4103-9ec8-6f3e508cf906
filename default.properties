####################公用部分#########################
#软件类型xiongzai或者kvenjoy，当是xiongzai或者不填时，直接选择熊仔书法家。当时kvenjoy时为奎享雕刻
machine=kvenjoy
#定死机器类型，当定死机器类型后，启动不会让选择机器。
#fixed_machine_type=Ebb
#自动连接
auto_connect=true
#绘图速度（毫米/分钟）
feed_rate=1500
#落笔后暂停时间（秒）
tool_on_delay=0.2
#抬笔前暂停时间（秒）
tool_off_delay=0.2
#点动速度（毫米/分钟）
jog_speed=2500
#抬笔高度
pen_up_pos=12000
#落笔高度
pen_down_pos=18000
#文件完成后自动休眠机器
autoSleep=false
#单步模式
single_step_command=false
#自动设置原点
auto_set_zero=true
#开始前自定义代码，多个命令用;分割
custom_start_gcode=
#结束后自定义代码，多个命令用;分割
custom_end_gcode=

######################ebb设置######################
#毫米转换为步进电机步数
mm_to_steps=100
#最大抬笔/落笔速度（相当于进度条最右边的值）
max_pen_speed=32767
#最大舵机转动角度（最大值可以到65535）
max_pen_value=30000
#默认舵机速度
servo_speed=750

###################grbl设置#######################
#笔控类型
grbl_type=Servo
#当笔控为步进电机时，落笔z位置，单位毫米。
zOn=2
#当笔控为步进电机时，抬笔z位置，单位毫米。
zOff=0
#步进电机抬笔速度设置
zSpeed=3000

##################marlin设置#####################
#笔控类型
marlin_type=Servo
#z轴落笔高度
marlin_pen_down_mm=2
#z轴抬笔高度
marlin_pen_up_mm=0
#舵机抬笔角度
marlin_pen_up=50
#舵机落笔角度
marlin_pen_down=0
#z轴下笔抬笔速度
marlin_z_speed=3000
#横坐标偏移
offset_x=0
#纵坐标偏移
offset_y=0

#####################坐标系设置#########################
#对调坐标系xy轴
axe_rev_xy=false
#反转坐标系x轴
axe_reverse_x=false
#反转坐标系y轴
axe_reverse_y=false
#坐标系位置（0左上、1右上、2左下、3右下）
axe_pos=0
#激光功率（参见grbl最大功率设置）
laser_power=1000